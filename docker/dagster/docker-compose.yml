services:
  # This service runs the postgres DB used by dagster for run storage, schedule storage,
  # and event log storage. Depending on the hardware you run this Compose on, you may be able
  # to reduce the interval and timeout in the healthcheck to speed up your `docker-compose up` times.
  database:
    image: postgres:15.2-alpine
    restart: always
    expose:
      - 5432
    volumes:
      - db:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${DAGSTER_DB_USER}
      - POSTGRES_PASSWORD=${DAGSTER_DB_PASSWORD}
      - POSTGRES_DB=dagster
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DAGSTER_DB_USER} -d dagster"]
      interval: 10s
      timeout: 8s
      retries: 5

  # This service runs the gRPC server that loads your user code, in both dagster-webserver
  # and dagster-daemon. By setting DAGSTER_CURRENT_IMAGE to its own image, we tell the
  # run launcher to use this same image when launching runs in a new container as well.
  # Multiple containers like this can be deployed separately - each just needs to run on
  # its own port, and have its own entry in the workspace.yaml file that's loaded by the
      # webserver.
  user-code:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/dagster:user-code-latest
    # build:
    #   context: .
    #   dockerfile: ./user-code/Dockerfile
    container_name: docker_example_user_code_image
    restart: always
    environment:
      DAGSTER_POSTGRES_USER: "${DAGSTER_DB_USER}"
      DAGSTER_POSTGRES_PASSWORD: "${DAGSTER_DB_PASSWORD}"
      DAGSTER_POSTGRES_DB: "dagster"
      DAGSTER_CURRENT_IMAGE: "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/dagster:user-code-latest"


  # This service runs dagster-webserver, which loads your user code from the user code container.
  # Since our instance uses the QueuedRunCoordinator, any runs submitted from the webserver will be put on
  # a queue and later dequeued and launched by dagster-daemon.
  webserver:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/dagster:dagster-latest
    # build:
    #   context: .
    #   dockerfile: ./dagster/Dockerfile
    entrypoint:
      - dagster-webserver
      - -h
      - "0.0.0.0"
      - -p
      - "3000"
      - -w
      - workspace.yaml
    expose:
      - "3000"
    ports:
      - "3000:3000"
    environment:
      DAGSTER_POSTGRES_USER: "${DAGSTER_DB_USER}"
      DAGSTER_POSTGRES_PASSWORD: "${DAGSTER_DB_PASSWORD}"
      DAGSTER_POSTGRES_DB: "dagster"
    volumes: # Make docker client accessible so we can terminate containers from the webserver
      - /var/run/docker.sock:/var/run/docker.sock
      - /tmp/io_manager_storage:/tmp/io_manager_storage
    depends_on:
      database:
        condition: service_healthy
      user-code:
        condition: service_started

  # This service runs the dagster-daemon process, which is responsible for taking runs
  # off of the queue and launching them, as well as creating runs from schedules or sensors.
  daemon:
    image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/dagster:dagster-latest
    # build:
    #   context: .
    #   dockerfile: ./dagster/Dockerfile
    entrypoint:
      - dagster-daemon
      - run
    restart: on-failure
    volumes: # Make docker client accessible so we can launch containers using host docker
      - /var/run/docker.sock:/var/run/docker.sock
      - /tmp/io_manager_storage:/tmp/io_manager_storage
    environment:
      - DAGSTER_POSTGRES_DB=dagster
      - DAGSTER_POSTGRES_USER=${DAGSTER_DB_USER}
      - DAGSTER_POSTGRES_PASSWORD=${DAGSTER_DB_PASSWORD}
    depends_on:
      database:
        condition: service_healthy
      user-code:
        condition: service_started


volumes:
  db:
networks:
  default:
    name: dagster-network
