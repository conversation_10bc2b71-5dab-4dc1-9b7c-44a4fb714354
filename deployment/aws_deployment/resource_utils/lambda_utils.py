from typing import Dict, List
from aws_cdk import (
    aws_lambda as lambda_,
    aws_iam as iam,
    Duration,
)
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def create_lambda_role_from_config(
    scope: Construct,
    role_config: Dict,
    secrets_arns: List[str] = None,
) -> iam.Role:
    """
    Create an IAM role for Lambda function from configuration.

    Args:
        scope: The CDK construct scope
        role_config: IAM role configuration
        secrets_arns: List of secret ARNs to grant access to

    Returns:
        The created IAM role
    """
    role_id = role_config.get("id")

    # Create the role with <PERSON><PERSON> as the principal
    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
        managed_policies=[
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "service-role/AWSLambdaBasicExecutionRole"
            ),
        ],
    )

    # Add the specific permissions required by the prefect operational bootstrap lambda
    lambda_policy = iam.PolicyStatement(
        effect=iam.Effect.ALLOW,
        actions=[
            "ec2:DescribeInstances",
            "ecs:DescribeTaskDefinition",
            "ssm:GetParameter",
            "ssm:GetParameters",
            "secretsmanager:GetSecretValue",
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents",
        ],
        resources=["*"],
    )
    role.add_to_policy(lambda_policy)

    # Add permissions to access specific secrets if provided
    if secrets_arns:
        secrets_policy = iam.PolicyStatement(
            actions=["secretsmanager:GetSecretValue", "secretsmanager:DescribeSecret"],
            resources=secrets_arns,
        )
        role.add_to_policy(secrets_policy)

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=role_config,
        value=role.role_arn,
    )

    return role


def create_lambda_from_config(
    scope: Construct,
    lambda_config: Dict,
    role: iam.Role,
) -> lambda_.Function:
    """
    Create a Lambda function from configuration using container image.

    Args:
        scope: The CDK construct scope
        lambda_config: Lambda function configuration
        role: IAM role for the Lambda function

    Returns:
        The created Lambda function
    """
    lambda_id = lambda_config.get("id")
    image_uri = lambda_config.get("image")

    # Create Lambda function from container image
    lambda_function = lambda_.Function(
        scope,
        lambda_id,
        function_name=lambda_id,
        code=lambda_.Code.from_ecr_image(image_uri),
        handler=lambda_.Handler.FROM_IMAGE,
        runtime=lambda_.Runtime.FROM_IMAGE,
        role=role,
        timeout=Duration.minutes(15),  # 15 minutes timeout for bootstrap operations
        memory_size=512,  # 512 MB memory
        environment={
            "AWS_CONFIG_PATH": "/app/shared/config/aws_config.yaml",
            "BOOTSTRAP_CONFIG_PATH": "/app/src/lambdas/prefect_operational_bootstrap/prefect_bootstrap_config.yaml",
        },
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=lambda_config,
        value=lambda_function.function_arn,
    )

    return lambda_function
